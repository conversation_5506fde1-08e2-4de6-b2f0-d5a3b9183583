import { NextResponse } from "next/server";

export function middleware(request: any) {
  const session = request.cookies.get("session");

  if (!session && isProtectedRoute(request.nextUrl.pathname)) {
    return NextResponse.redirect(new URL("/login", request.url));
  }

  return NextResponse.next();
}

function isProtectedRoute(pathname: string) {
  const protectedPaths = ["/dashboard(.*)"];
  return protectedPaths.some((path) => pathname.startsWith(path));
}

export const config = {
  matcher: ["/((?!api|_next/static|_next/image|favicon.ico|login|register).*)"],
};
