import { buttonVariants } from "@/components/ui/button";
import { siteConfig } from "@/config/site";
import { cn } from "@/lib/utils";
import { SignIn as ClerkSignInForm } from "@clerk/nextjs";
import { Brain } from "lucide-react";
import Link from "next/link";
import SignInForm from "./form/sign-in-form";

type AuthMode = "sign-in" | "sign-up";

interface AuthViewProps {
  mode: AuthMode;
}

export default function AuthView({ mode }: AuthViewProps) {
  const isSignUp = mode === "sign-up";

  const getTestimonial = () => {
    if (isSignUp) {
      return {
        quote:
          "This starter template has saved me countless hours of work and helped me deliver projects to my clients faster than ever before.",
        author: "Random Dude",
      };
    } else {
      return {
        quote:
          "The authentication system is seamless and the developer experience is outstanding. Highly recommended!",
        author: "Happy Developer",
      };
    }
  };

  const testimonial = getTestimonial();

  return (
    <div className="relative h-screen flex-col items-center justify-center md:grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <Link
        href={isSignUp ? "/auth/sign-in" : "/auth/sign-up"}
        className={cn(
          buttonVariants({ variant: "ghost" }),
          "absolute top-4 right-4 hidden md:top-8 md:right-8"
        )}
      >
        {isSignUp ? "Sign In" : "Sign Up"}
      </Link>

      <div className="bg-muted relative hidden h-full flex-col p-10 lg:flex dark:border-r">
        <div className="absolute inset-0" />
        <div className="relative z-20 flex items-center text-lg font-medium gap-2">
          <Brain />
          {siteConfig.name}
        </div>

        <div className="relative z-20 mt-auto">
          <blockquote className="space-y-2">
            <p className="text-lg">&ldquo;{testimonial.quote}&rdquo;</p>
            <footer className="text-sm">{testimonial.author}</footer>
          </blockquote>
        </div>
      </div>

      <div className="flex h-full items-center justify-center p-4 lg:p-8">
        <div className="flex w-full max-w-md flex-col items-center justify-center space-y-6">
          {isSignUp ? <SignInForm /> : <ClerkSignInForm />}
        </div>
      </div>
    </div>
  );
}
